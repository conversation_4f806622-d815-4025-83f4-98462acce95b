import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import AssessmentPage from '@/app/assessment/page'

// Mock the fetch function
global.fetch = jest.fn()

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// Mock browser APIs for file download
global.URL.createObjectURL = jest.fn(() => 'mock-url')
global.URL.revokeObjectURL = jest.fn()
global.Blob = jest.fn().mockImplementation((content, options) => ({
  content,
  options,
  type: options?.type || 'text/plain'
}))

// Mock document.createElement for download links
const mockAnchorElement = {
  href: '',
  download: '',
  click: jest.fn(),
  style: { display: '' }
}
document.createElement = jest.fn().mockImplementation((tagName) => {
  if (tagName === 'a') {
    return mockAnchorElement
  }
  return {}
})
document.body.appendChild = jest.fn()
document.body.removeChild = jest.fn()

// Mock the assessment components
jest.mock('@/components/assessment/DemographicsSection', () => {
  return function MockDemographicsSection({ data, onUpdate }: any) {
    return (
      <div data-testid="demographics-section">
        <input 
          data-testid="demographics-input"
          onChange={(e) => onUpdate({ firstName: e.target.value })}
        />
      </div>
    )
  }
})

jest.mock('@/components/assessment/SymptomsSection', () => {
  return function MockSymptomsSection({ data, onUpdate }: any) {
    return (
      <div data-testid="symptoms-section">
        <input 
          data-testid="symptoms-input"
          onChange={(e) => onUpdate({ symptom1: e.target.value })}
        />
      </div>
    )
  }
})

jest.mock('@/components/assessment/RiskAssessmentSection', () => {
  return function MockRiskAssessmentSection({ data, onUpdate }: any) {
    return <div data-testid="risk-section">Risk Assessment</div>
  }
})

jest.mock('@/components/assessment/MedicalHistorySection', () => {
  return function MockMedicalHistorySection({ data, onUpdate }: any) {
    return <div data-testid="history-section">Medical History</div>
  }
})

jest.mock('@/components/assessment/MentalStatusExamSection', () => {
  return function MockMentalStatusExamSection({ data, onUpdate }: any) {
    return <div data-testid="mental-status-section">Mental Status</div>
  }
})

jest.mock('@/components/assessment/DiagnosisSection', () => {
  return function MockDiagnosisSection({ data, onUpdate }: any) {
    return <div data-testid="diagnosis-section">Diagnosis</div>
  }
})

describe('AssessmentPage', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
    ;(fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: async () => ({ success: true })
    })
  })

  it('renders the assessment page with all sections', () => {
    render(<AssessmentPage />)
    
    expect(screen.getByText('Psychiatric Assessment')).toBeInTheDocument()
    expect(screen.getByText('Demographics')).toBeInTheDocument()
    expect(screen.getByText('Symptoms')).toBeInTheDocument()
    expect(screen.getByText('Risk Assessment')).toBeInTheDocument()
    expect(screen.getByText('Medical History')).toBeInTheDocument()
    expect(screen.getByText('Mental Status')).toBeInTheDocument()
    expect(screen.getByText('Diagnosis')).toBeInTheDocument()
  })

  it('loads saved data from localStorage on mount', () => {
    const savedData = {
      data: { demographics: { firstName: 'John' } },
      completedSections: ['demographics'],
      lastSaved: new Date().toISOString()
    }
    localStorageMock.getItem.mockReturnValue(JSON.stringify(savedData))
    
    render(<AssessmentPage />)
    
    expect(localStorageMock.getItem).toHaveBeenCalledWith('psychiatric-assessment-data')
  })

  it('shows progress based on completed sections', async () => {
    render(<AssessmentPage />)
    
    // Initially should show 0% progress
    expect(screen.getByRole('progressbar')).toBeInTheDocument()
  })

  it('auto-saves data after changes', async () => {
    const user = userEvent.setup()
    render(<AssessmentPage />)
    
    const demographicsInput = screen.getByTestId('demographics-input')
    await user.type(demographicsInput, 'John')
    
    // Wait for auto-save (2 second debounce)
    await waitFor(() => {
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'psychiatric-assessment-data',
        expect.stringContaining('John')
      )
    }, { timeout: 3000 })
  })

  it('handles export functionality', async () => {
    const user = userEvent.setup()
    render(<AssessmentPage />)
    
    const exportButton = screen.getByText('Export JSON')
    await user.click(exportButton)

    // Should show export options
    expect(screen.getByText('Export JSON')).toBeInTheDocument()
    expect(screen.getByText('Export CSV')).toBeInTheDocument()
  })
})
