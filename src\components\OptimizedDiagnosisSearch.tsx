"use client";

import React, { useState, useMemo, useCallback } from 'react';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Search, Plus, X, Stethoscope } from 'lucide-react';

interface Diagnosis {
  id: string;
  code: string;
  name: string;
  category: string;
  description: string;
  criteria: string[];
}

interface OptimizedDiagnosisSearchProps {
  diagnoses: Diagnosis[];
  selectedDiagnoses: string[];
  onDiagnosesChange: (selectedIds: string[]) => void;
  maxSelections?: number;
}

export const OptimizedDiagnosisSearch: React.FC<OptimizedDiagnosisSearchProps> = ({
  diagnoses,
  selectedDiagnoses,
  onDiagnosesChange,
  maxSelections = 5
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showCriteria, setShowCriteria] = useState<string | null>(null);

  // Get unique categories
  const categories = useMemo(() => {
    const cats = Array.from(new Set(diagnoses.map(d => d.category)));
    return cats.sort();
  }, [diagnoses]);

  // Filter diagnoses based on search and category
  const filteredDiagnoses = useMemo(() => {
    return diagnoses.filter(diagnosis => {
      const matchesSearch = searchTerm === '' || 
        diagnosis.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        diagnosis.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
        diagnosis.description.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesCategory = selectedCategory === 'all' || 
        diagnosis.category === selectedCategory;

      return matchesSearch && matchesCategory;
    });
  }, [diagnoses, searchTerm, selectedCategory]);

  // Get selected diagnosis objects
  const selectedDiagnosisObjects = useMemo(() => {
    return diagnoses.filter(d => selectedDiagnoses.includes(d.id));
  }, [diagnoses, selectedDiagnoses]);

  // Handle diagnosis selection
  const handleDiagnosisSelect = useCallback((diagnosisId: string) => {
    if (selectedDiagnoses.includes(diagnosisId)) {
      onDiagnosesChange(selectedDiagnoses.filter(id => id !== diagnosisId));
    } else if (selectedDiagnoses.length < maxSelections) {
      onDiagnosesChange([...selectedDiagnoses, diagnosisId]);
    }
  }, [selectedDiagnoses, onDiagnosesChange, maxSelections]);

  // Remove diagnosis
  const handleRemoveDiagnosis = useCallback((diagnosisId: string) => {
    onDiagnosesChange(selectedDiagnoses.filter(id => id !== diagnosisId));
  }, [selectedDiagnoses, onDiagnosesChange]);

  // Group diagnoses by category for better organization
  const groupedDiagnoses = useMemo(() => {
    const groups = filteredDiagnoses.reduce((acc, diagnosis) => {
      if (!acc[diagnosis.category]) {
        acc[diagnosis.category] = [];
      }
      acc[diagnosis.category].push(diagnosis);
      return acc;
    }, {} as Record<string, Diagnosis[]>);

    // Sort within each group
    Object.keys(groups).forEach(category => {
      groups[category].sort((a, b) => a.name.localeCompare(b.name));
    });

    return groups;
  }, [filteredDiagnoses]);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Stethoscope className="h-5 w-5" />
            <span>Diagnosis Selection</span>
          </div>
          <Badge variant="outline">
            {selectedDiagnoses.length}/{maxSelections} selected
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Selected Diagnoses */}
        {selectedDiagnosisObjects.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-gray-700">Selected Diagnoses:</h4>
            <div className="flex flex-wrap gap-2">
              {selectedDiagnosisObjects.map(diagnosis => (
                <Badge
                  key={diagnosis.id}
                  variant="secondary"
                  className="flex items-center space-x-1 px-3 py-1"
                >
                  <span>{diagnosis.code} - {diagnosis.name}</span>
                  <button
                    onClick={() => handleRemoveDiagnosis(diagnosis.id)}
                    className="ml-1 hover:text-red-600"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Search and Category Filter */}
        <div className="space-y-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search diagnoses by name, code, or description..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
          >
            <option value="all">All Categories</option>
            {categories.map(category => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>
        </div>

        {/* Search Results */}
        <div className="border rounded-lg max-h-96 overflow-y-auto">
          {Object.keys(groupedDiagnoses).length > 0 ? (
            <div className="p-2">
              {Object.entries(groupedDiagnoses).map(([category, categoryDiagnoses]) => (
                <div key={category} className="mb-4">
                  <h4 className="text-sm font-semibold text-gray-700 mb-2 px-2">
                    {category} ({categoryDiagnoses.length})
                  </h4>
                  <div className="space-y-1">
                    {categoryDiagnoses.map(diagnosis => {
                      const isSelected = selectedDiagnoses.includes(diagnosis.id);
                      const canSelect = selectedDiagnoses.length < maxSelections || isSelected;
                      
                      return (
                        <div
                          key={diagnosis.id}
                          className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                            isSelected
                              ? 'bg-blue-50 border-blue-200'
                              : canSelect
                              ? 'hover:bg-gray-50 border-gray-200'
                              : 'opacity-50 cursor-not-allowed border-gray-100'
                          }`}
                          onClick={() => canSelect && handleDiagnosisSelect(diagnosis.id)}
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center space-x-2 mb-1">
                                <span className="font-medium text-sm">{diagnosis.code}</span>
                                <span className="text-sm">{diagnosis.name}</span>
                                {isSelected && (
                                  <Badge variant="default" className="text-xs">
                                    Selected
                                  </Badge>
                                )}
                              </div>
                              <p className="text-xs text-gray-600 mb-2">
                                {diagnosis.description}
                              </p>
                              {diagnosis.criteria && diagnosis.criteria.length > 0 && (
                                <div className="text-xs">
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setShowCriteria(showCriteria === diagnosis.id ? null : diagnosis.id);
                                    }}
                                    className="text-blue-600 hover:text-blue-800"
                                  >
                                    {showCriteria === diagnosis.id ? 'Hide' : 'Show'} Criteria
                                  </button>
                                  {showCriteria === diagnosis.id && (
                                    <ul className="mt-2 space-y-1 text-gray-600">
                                      {diagnosis.criteria.map((criterion, index) => (
                                        <li key={index} className="flex items-start">
                                          <span className="mr-2">•</span>
                                          <span>{criterion}</span>
                                        </li>
                                      ))}
                                    </ul>
                                  )}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex items-center justify-center h-32 text-gray-500">
              No diagnoses found matching your criteria
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
