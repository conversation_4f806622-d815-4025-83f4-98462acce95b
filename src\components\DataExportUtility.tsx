"use client";

import React, { useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Download, FileText, Database, CheckCircle, AlertCircle } from 'lucide-react';

interface ExportData {
  assessments: any[];
  totalRecords: number;
  lastUpdated: string;
}

interface DataExportUtilityProps {
  data: ExportData;
  onExport?: (format: 'csv' | 'json') => void;
}

export const DataExportUtility: React.FC<DataExportUtilityProps> = ({
  data,
  onExport
}) => {
  const [isExporting, setIsExporting] = useState(false);
  const [exportStatus, setExportStatus] = useState<{
    type: 'success' | 'error' | null;
    message: string;
  }>({ type: null, message: '' });

  const handleExport = useCallback(async (format: 'csv' | 'json') => {
    setIsExporting(true);
    setExportStatus({ type: null, message: '' });

    try {
      if (onExport) {
        await onExport(format);
      } else {
        // Default export functionality
        await exportData(format, data);
      }
      
      setExportStatus({
        type: 'success',
        message: `Data successfully exported as ${format.toUpperCase()}`
      });
    } catch (error) {
      setExportStatus({
        type: 'error',
        message: `Failed to export data: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    } finally {
      setIsExporting(false);
    }
  }, [data, onExport]);

  const exportData = async (format: 'csv' | 'json', exportData: ExportData) => {
    const { assessments } = exportData;
    
    if (format === 'json') {
      const jsonData = JSON.stringify(assessments, null, 2);
      downloadFile(jsonData, 'psychiatric-assessments.json', 'application/json');
    } else if (format === 'csv') {
      const csvData = convertToCSV(assessments);
      downloadFile(csvData, 'psychiatric-assessments.csv', 'text/csv');
    }
  };

  const convertToCSV = (assessments: any[]) => {
    if (assessments.length === 0) return '';

    // Get all unique keys from all assessments
    const allKeys = new Set<string>();
    assessments.forEach(assessment => {
      Object.keys(flattenObject(assessment)).forEach(key => allKeys.add(key));
    });

    const headers = Array.from(allKeys);
    const csvRows = [headers.join(',')];

    assessments.forEach(assessment => {
      const flatAssessment = flattenObject(assessment);
      const row = headers.map(header => {
        const value = flatAssessment[header] || '';
        // Escape commas and quotes in CSV
        return typeof value === 'string' && (value.includes(',') || value.includes('"'))
          ? `"${value.replace(/"/g, '""')}"`
          : value;
      });
      csvRows.push(row.join(','));
    });

    return csvRows.join('\n');
  };

  const flattenObject = (obj: any, prefix = ''): Record<string, any> => {
    const flattened: Record<string, any> = {};
    
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const newKey = prefix ? `${prefix}.${key}` : key;
        
        if (obj[key] === null || obj[key] === undefined) {
          flattened[newKey] = '';
        } else if (Array.isArray(obj[key])) {
          flattened[newKey] = obj[key].map((item: any) => 
            typeof item === 'object' ? JSON.stringify(item) : item
          ).join('; ');
        } else if (typeof obj[key] === 'object') {
          Object.assign(flattened, flattenObject(obj[key], newKey));
        } else {
          flattened[newKey] = obj[key];
        }
      }
    }
    
    return flattened;
  };

  const downloadFile = (content: string, filename: string, mimeType: string) => {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const formatFileSize = (assessments: any[]) => {
    const jsonSize = JSON.stringify(assessments).length;
    const kb = Math.round(jsonSize / 1024);
    return kb > 1024 ? `${Math.round(kb / 1024)} MB` : `${kb} KB`;
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Database className="h-5 w-5" />
          <span>Data Export</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Export Status */}
        {exportStatus.type && (
          <Alert variant={exportStatus.type === 'error' ? 'destructive' : 'default'}>
            {exportStatus.type === 'success' ? (
              <CheckCircle className="h-4 w-4" />
            ) : (
              <AlertCircle className="h-4 w-4" />
            )}
            <AlertDescription>{exportStatus.message}</AlertDescription>
          </Alert>
        )}

        {/* Data Summary */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">{data.totalRecords}</div>
            <div className="text-sm text-gray-600">Total Records</div>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">{formatFileSize(data.assessments)}</div>
            <div className="text-sm text-gray-600">Estimated Size</div>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">2</div>
            <div className="text-sm text-gray-600">Export Formats</div>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-sm font-medium text-gray-900">Last Updated</div>
            <div className="text-xs text-gray-600">
              {new Date(data.lastUpdated).toLocaleDateString()}
            </div>
          </div>
        </div>

        {/* Export Options */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-gray-700">Export Formats:</h4>
          
          <div className="grid md:grid-cols-2 gap-4">
            {/* JSON Export */}
            <div className="border rounded-lg p-4 space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <FileText className="h-5 w-5 text-blue-600" />
                  <span className="font-medium">JSON Format</span>
                </div>
                <Badge variant="outline">Structured</Badge>
              </div>
              <p className="text-sm text-gray-600">
                Complete data structure with nested objects. Ideal for applications and further processing.
              </p>
              <ul className="text-xs text-gray-500 space-y-1">
                <li>• Preserves data relationships</li>
                <li>• Machine-readable format</li>
                <li>• Supports complex data types</li>
              </ul>
              <Button
                onClick={() => handleExport('json')}
                disabled={isExporting || data.totalRecords === 0}
                className="w-full"
                variant="outline"
              >
                <Download className="h-4 w-4 mr-2" />
                {isExporting ? 'Exporting...' : 'Export JSON'}
              </Button>
            </div>

            {/* CSV Export */}
            <div className="border rounded-lg p-4 space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <FileText className="h-5 w-5 text-green-600" />
                  <span className="font-medium">CSV Format</span>
                </div>
                <Badge variant="outline">Tabular</Badge>
              </div>
              <p className="text-sm text-gray-600">
                Flattened tabular format. Perfect for spreadsheets, analysis, and ML training.
              </p>
              <ul className="text-xs text-gray-500 space-y-1">
                <li>• Excel/spreadsheet compatible</li>
                <li>• ML training ready</li>
                <li>• Statistical analysis friendly</li>
              </ul>
              <Button
                onClick={() => handleExport('csv')}
                disabled={isExporting || data.totalRecords === 0}
                className="w-full"
                variant="outline"
              >
                <Download className="h-4 w-4 mr-2" />
                {isExporting ? 'Exporting...' : 'Export CSV'}
              </Button>
            </div>
          </div>
        </div>

        {/* Export Notes */}
        <div className="text-xs text-gray-500 space-y-1">
          <p>• Exported files include all assessment data with timestamps</p>
          <p>• Personal identifiers are included - ensure compliance with privacy regulations</p>
          <p>• Data is exported in the current state without additional validation</p>
        </div>
      </CardContent>
    </Card>
  );
};
