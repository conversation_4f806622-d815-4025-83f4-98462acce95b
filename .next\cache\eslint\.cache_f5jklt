[{"C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\app\\api\\assessments\\route.ts": "1", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\app\\api\\export\\route.ts": "2", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\app\\assessment\\page.tsx": "3", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\app\\data\\page.tsx": "4", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\app\\layout.tsx": "5", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\app\\page.tsx": "6", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\app\\test\\page.tsx": "7", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\assessment\\DemographicsSection.tsx": "8", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\assessment\\DiagnosisSection.tsx": "9", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\assessment\\MedicalHistorySection.tsx": "10", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\assessment\\MentalStatusExamSection.tsx": "11", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\assessment\\RiskAssessmentSection.tsx": "12", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\assessment\\SymptomsSection.tsx": "13", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\badge.tsx": "14", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\button.tsx": "15", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\card.tsx": "16", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\checkbox.tsx": "17", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\input.tsx": "18", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\label.tsx": "19", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\progress.tsx": "20", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\radio-group.tsx": "21", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\select.tsx": "22", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\tabs.tsx": "23", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\textarea.tsx": "24", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\lib\\constants.ts": "25", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\lib\\db.ts": "26", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\lib\\utils.ts": "27"}, {"size": 6081, "mtime": 1754426989138, "results": "28", "hashOfConfig": "29"}, {"size": 5804, "mtime": 1754427039463, "results": "30", "hashOfConfig": "29"}, {"size": 10587, "mtime": 1754428241244, "results": "31", "hashOfConfig": "29"}, {"size": 10964, "mtime": 1754427152912, "results": "32", "hashOfConfig": "29"}, {"size": 648, "mtime": 1754423739859, "results": "33", "hashOfConfig": "29"}, {"size": 12455, "mtime": 1754427199620, "results": "34", "hashOfConfig": "29"}, {"size": 217, "mtime": 1754426644344, "results": "35", "hashOfConfig": "29"}, {"size": 15276, "mtime": 1754425894471, "results": "36", "hashOfConfig": "29"}, {"size": 10758, "mtime": 1754428063173, "results": "37", "hashOfConfig": "29"}, {"size": 8928, "mtime": 1754426043763, "results": "38", "hashOfConfig": "29"}, {"size": 17029, "mtime": 1754428077125, "results": "39", "hashOfConfig": "29"}, {"size": 13262, "mtime": 1754426002181, "results": "40", "hashOfConfig": "29"}, {"size": 8050, "mtime": 1754425936241, "results": "41", "hashOfConfig": "29"}, {"size": 1128, "mtime": 1754426910637, "results": "42", "hashOfConfig": "29"}, {"size": 1835, "mtime": 1754423284482, "results": "43", "hashOfConfig": "29"}, {"size": 1877, "mtime": 1754423327245, "results": "44", "hashOfConfig": "29"}, {"size": 1056, "mtime": 1754425731778, "results": "45", "hashOfConfig": "29"}, {"size": 824, "mtime": 1754423296828, "results": "46", "hashOfConfig": "29"}, {"size": 710, "mtime": 1754423480970, "results": "47", "hashOfConfig": "29"}, {"size": 777, "mtime": 1754425756984, "results": "48", "hashOfConfig": "29"}, {"size": 1467, "mtime": 1754425745873, "results": "49", "hashOfConfig": "29"}, {"size": 5615, "mtime": 1754423533159, "results": "50", "hashOfConfig": "29"}, {"size": 1883, "mtime": 1754423400668, "results": "51", "hashOfConfig": "29"}, {"size": 772, "mtime": 1754425719167, "results": "52", "hashOfConfig": "29"}, {"size": 2332, "mtime": 1754423249946, "results": "53", "hashOfConfig": "29"}, {"size": 300, "mtime": 1754423209607, "results": "54", "hashOfConfig": "29"}, {"size": 2222, "mtime": 1754423194540, "results": "55", "hashOfConfig": "29"}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1wqmyzj", {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\app\\api\\assessments\\route.ts", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\app\\api\\export\\route.ts", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\app\\assessment\\page.tsx", ["137"], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\app\\data\\page.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\app\\test\\page.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\assessment\\DemographicsSection.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\assessment\\DiagnosisSection.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\assessment\\MedicalHistorySection.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\assessment\\MentalStatusExamSection.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\assessment\\RiskAssessmentSection.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\assessment\\SymptomsSection.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\checkbox.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\progress.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\radio-group.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\tabs.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\lib\\constants.ts", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\lib\\db.ts", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\lib\\utils.ts", [], [], {"ruleId": "138", "severity": 1, "message": "139", "line": 122, "column": 6, "nodeType": "140", "endLine": 122, "endColumn": 8, "suggestions": "141"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'assessmentData'. Either include it or remove the dependency array. You can also do a functional update 'setAssessmentData(a => ...)' if you only need 'assessmentData' in the 'setAssessmentData' call.", "ArrayExpression", ["142"], {"desc": "143", "fix": "144"}, "Update the dependencies array to be: [assessmentData]", {"range": "145", "text": "146"}, [4279, 4281], "[assessmentData]"]