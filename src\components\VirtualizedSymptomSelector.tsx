"use client";

import React, { useState, useMemo, useCallback } from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Search, Filter } from 'lucide-react';

interface Symptom {
  id: string;
  name: string;
  category: string;
  severity: 'mild' | 'moderate' | 'severe';
  description: string;
}

interface VirtualizedSymptomSelectorProps {
  symptoms: Symptom[];
  selectedSymptoms: string[];
  onSymptomsChange: (selectedIds: string[]) => void;
  height?: number;
}

const SymptomItem: React.FC<{
  symptom: Symptom;
  isSelected: boolean;
  onToggle: (id: string) => void;
}> = ({ symptom, isSelected, onToggle }) => {
  const severityColors = {
    mild: 'bg-green-100 text-green-800',
    moderate: 'bg-yellow-100 text-yellow-800',
    severe: 'bg-red-100 text-red-800'
  };

  return (
    <div className="border-b border-gray-100 hover:bg-gray-50">
      <div className="p-3 flex items-start space-x-3">
        <Checkbox
          checked={isSelected}
          onCheckedChange={() => onToggle(symptom.id)}
          className="mt-1"
        />
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-1">
            <h4 className="text-sm font-medium text-gray-900 truncate">
              {symptom.name}
            </h4>
            <Badge 
              variant="secondary" 
              className={`text-xs ${severityColors[symptom.severity]}`}
            >
              {symptom.severity}
            </Badge>
          </div>
          <p className="text-xs text-gray-600 mb-1">
            {symptom.category}
          </p>
          {symptom.description && (
            <p className="text-xs text-gray-500 line-clamp-2">
              {symptom.description}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export const VirtualizedSymptomSelector: React.FC<VirtualizedSymptomSelectorProps> = ({
  symptoms,
  selectedSymptoms,
  onSymptomsChange,
  height = 400
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedSeverity, setSelectedSeverity] = useState<string>('all');

  // Get unique categories and severities
  const categories = useMemo(() => {
    const cats = Array.from(new Set(symptoms.map(s => s.category)));
    return cats.sort();
  }, [symptoms]);

  const severities = ['mild', 'moderate', 'severe'];

  // Filter symptoms based on search and filters
  const filteredSymptoms = useMemo(() => {
    return symptoms.filter(symptom => {
      const matchesSearch = searchTerm === '' || 
        symptom.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        symptom.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        symptom.category.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesCategory = selectedCategory === 'all' || 
        symptom.category === selectedCategory;
      
      const matchesSeverity = selectedSeverity === 'all' || 
        symptom.severity === selectedSeverity;

      return matchesSearch && matchesCategory && matchesSeverity;
    });
  }, [symptoms, searchTerm, selectedCategory, selectedSeverity]);

  // Handle symptom selection
  const handleSymptomToggle = useCallback((symptomId: string) => {
    const newSelected = selectedSymptoms.includes(symptomId)
      ? selectedSymptoms.filter(id => id !== symptomId)
      : [...selectedSymptoms, symptomId];
    onSymptomsChange(newSelected);
  }, [selectedSymptoms, onSymptomsChange]);

  // Select all visible symptoms
  const handleSelectAllVisible = () => {
    const visibleIds = filteredSymptoms.map(s => s.id);
    const newSelected = Array.from(new Set([...selectedSymptoms, ...visibleIds]));
    onSymptomsChange(newSelected);
  };

  // Clear all visible symptoms
  const handleClearAllVisible = () => {
    const visibleIds = new Set(filteredSymptoms.map(s => s.id));
    const newSelected = selectedSymptoms.filter(id => !visibleIds.has(id));
    onSymptomsChange(newSelected);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Filter className="h-5 w-5" />
          <span>Symptom Selection</span>
          <Badge variant="outline">
            {selectedSymptoms.length} selected
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search and Filters */}
        <div className="space-y-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search symptoms..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <div className="flex flex-wrap gap-2">
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm"
            >
              <option value="all">All Categories</option>
              {categories.map(category => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>
            
            <select
              value={selectedSeverity}
              onChange={(e) => setSelectedSeverity(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm"
            >
              <option value="all">All Severities</option>
              {severities.map(severity => (
                <option key={severity} value={severity}>
                  {severity.charAt(0).toUpperCase() + severity.slice(1)}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Bulk Actions */}
        <div className="flex justify-between items-center text-sm">
          <span className="text-gray-600">
            Showing {filteredSymptoms.length} of {symptoms.length} symptoms
          </span>
          <div className="space-x-2">
            <button
              onClick={handleSelectAllVisible}
              className="text-blue-600 hover:text-blue-800"
            >
              Select All Visible
            </button>
            <button
              onClick={handleClearAllVisible}
              className="text-red-600 hover:text-red-800"
            >
              Clear All Visible
            </button>
          </div>
        </div>

        {/* Symptom List */}
        <div className="border rounded-lg max-h-96 overflow-y-auto">
          {filteredSymptoms.length > 0 ? (
            filteredSymptoms.map(symptom => (
              <SymptomItem
                key={symptom.id}
                symptom={symptom}
                isSelected={selectedSymptoms.includes(symptom.id)}
                onToggle={handleSymptomToggle}
              />
            ))
          ) : (
            <div className="flex items-center justify-center h-32 text-gray-500">
              No symptoms found matching your criteria
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
